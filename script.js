// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 1200,
    once: true,
    offset: 100,
    easing: 'ease-out-cubic'
});

// Initialize GSAP
gsap.registerPlugin(ScrollTrigger);

// Initialize Particles.js
function initParticles() {
    particlesJS('particles-js', {
        particles: {
            number: {
                value: 80,
                density: {
                    enable: true,
                    value_area: 800
                }
            },
            color: {
                value: ['#3b82f6', '#8b5cf6', '#06b6d4', '#10b981']
            },
            shape: {
                type: 'circle',
                stroke: {
                    width: 0,
                    color: '#000000'
                }
            },
            opacity: {
                value: 0.3,
                random: true,
                anim: {
                    enable: true,
                    speed: 1,
                    opacity_min: 0.1,
                    sync: false
                }
            },
            size: {
                value: 3,
                random: true,
                anim: {
                    enable: true,
                    speed: 2,
                    size_min: 0.1,
                    sync: false
                }
            },
            line_linked: {
                enable: true,
                distance: 150,
                color: '#3b82f6',
                opacity: 0.2,
                width: 1
            },
            move: {
                enable: true,
                speed: 1,
                direction: 'none',
                random: false,
                straight: false,
                out_mode: 'out',
                bounce: false,
                attract: {
                    enable: false,
                    rotateX: 600,
                    rotateY: 1200
                }
            }
        },
        interactivity: {
            detect_on: 'canvas',
            events: {
                onhover: {
                    enable: true,
                    mode: 'repulse'
                },
                onclick: {
                    enable: true,
                    mode: 'push'
                },
                resize: true
            },
            modes: {
                grab: {
                    distance: 400,
                    line_linked: {
                        opacity: 1
                    }
                },
                bubble: {
                    distance: 400,
                    size: 40,
                    duration: 2,
                    opacity: 8,
                    speed: 3
                },
                repulse: {
                    distance: 200,
                    duration: 0.4
                },
                push: {
                    particles_nb: 4
                },
                remove: {
                    particles_nb: 2
                }
            }
        },
        retina_detect: true
    });
}

// Advanced GSAP Animations
function initGSAPAnimations() {
    // Hero text animations
    const tl = gsap.timeline();

    tl.from('.hero-badge', {
        duration: 1,
        y: -50,
        opacity: 0,
        ease: 'bounce.out'
    })
    .from('.hero-title', {
        duration: 1.2,
        y: 50,
        opacity: 0,
        ease: 'power3.out'
    }, '-=0.5')
    .from('.hero-description', {
        duration: 1,
        y: 30,
        opacity: 0,
        ease: 'power2.out'
    }, '-=0.3')
    .from('.hero-buttons .btn', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        stagger: 0.2,
        ease: 'back.out(1.7)'
    }, '-=0.2')
    .from('.hero-social .social-icon', {
        duration: 0.6,
        scale: 0,
        opacity: 0,
        stagger: 0.1,
        ease: 'back.out(1.7)'
    }, '-=0.3');

    // Floating icons animation
    gsap.from('.floating-icon', {
        duration: 1.5,
        scale: 0,
        rotation: 180,
        opacity: 0,
        stagger: 0.2,
        ease: 'elastic.out(1, 0.5)',
        delay: 1
    });

    // Scroll-triggered animations
    gsap.utils.toArray('.project-card').forEach((card, i) => {
        gsap.from(card, {
            scrollTrigger: {
                trigger: card,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            },
            duration: 1,
            y: 100,
            opacity: 0,
            rotation: 5,
            ease: 'power3.out',
            delay: i * 0.1
        });
    });

    // Skills animation
    gsap.utils.toArray('.skill-item').forEach((skill, i) => {
        gsap.from(skill, {
            scrollTrigger: {
                trigger: skill,
                start: 'top 85%',
                end: 'bottom 15%',
                toggleActions: 'play none none reverse'
            },
            duration: 0.8,
            x: -50,
            opacity: 0,
            ease: 'power2.out',
            delay: i * 0.1
        });
    });

    // Timeline animation
    gsap.from('.timeline-item', {
        scrollTrigger: {
            trigger: '.timeline',
            start: 'top 70%',
            end: 'bottom 30%',
            toggleActions: 'play none none reverse'
        },
        duration: 1.2,
        x: 100,
        opacity: 0,
        ease: 'power3.out'
    });
}

// Theme Toggle Functionality
const themeToggle = document.getElementById('theme-toggle');
const body = document.body;

// Check for saved theme preference or default to light mode
const currentTheme = localStorage.getItem('theme') || 'light';
if (currentTheme === 'dark') {
    body.setAttribute('data-theme', 'dark');
    themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
}

themeToggle.addEventListener('click', () => {
    const currentTheme = body.getAttribute('data-theme');
    if (currentTheme === 'dark') {
        body.removeAttribute('data-theme');
        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        localStorage.setItem('theme', 'light');
    } else {
        body.setAttribute('data-theme', 'dark');
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        localStorage.setItem('theme', 'dark');
    }
});

// Mobile Navigation Toggle
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');

navToggle.addEventListener('click', () => {
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('active');
    });
});

// Typing Animation
const typingText = document.getElementById('typing-text');
const texts = [
    'Data Scientist Engineer',
    'Machine Learning Expert',
    'Deep Learning Specialist',
    'AI Solutions Developer',
    'NLP Enthusiast'
];

let textIndex = 0;
let charIndex = 0;
let isDeleting = false;

function typeWriter() {
    const currentText = texts[textIndex];
    
    if (isDeleting) {
        typingText.textContent = currentText.substring(0, charIndex - 1);
        charIndex--;
    } else {
        typingText.textContent = currentText.substring(0, charIndex + 1);
        charIndex++;
    }
    
    let typeSpeed = isDeleting ? 50 : 100;
    
    if (!isDeleting && charIndex === currentText.length) {
        typeSpeed = 2000; // Pause at end
        isDeleting = true;
    } else if (isDeleting && charIndex === 0) {
        isDeleting = false;
        textIndex = (textIndex + 1) % texts.length;
        typeSpeed = 500; // Pause before next text
    }
    
    setTimeout(typeWriter, typeSpeed);
}

// Start typing animation
typeWriter();

// Navbar scroll effect
window.addEventListener('scroll', () => {
    const navbar = document.getElementById('navbar');
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        if (body.getAttribute('data-theme') === 'dark') {
            navbar.style.background = 'rgba(17, 24, 39, 0.98)';
        }
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        if (body.getAttribute('data-theme') === 'dark') {
            navbar.style.background = 'rgba(17, 24, 39, 0.95)';
        }
    }
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            const navbarHeight = document.getElementById('navbar').offsetHeight;
            const targetPosition = target.offsetTop - navbarHeight - 20;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    });
});

// Scroll indicator functionality
function initScrollIndicator() {
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', () => {
            const aboutSection = document.getElementById('about');
            if (aboutSection) {
                const navbarHeight = document.getElementById('navbar').offsetHeight;
                const targetPosition = aboutSection.offsetTop - navbarHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    }
}

// Active navigation link highlighting
window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Add projects to the projects section
function loadProjects() {
    const projectsGrid = document.querySelector('.projects-grid');

    if (!projectsGrid) {
        console.error('Projects grid not found');
        return;
    }

    // Clear existing content
    projectsGrid.innerHTML = '';

    const projects = [
        {
            title: 'Generative AI with LangChain',
            description: 'Advanced Generative AI system with multilingual support and RAG architecture for data interaction.',
            features: [
                'Built RAG architecture with LangChain',
                'Natural language understanding for Q&A',
                'Multilingual speech recognition and synthesis',
                'Support for Tunisian dialect'
            ],
            technologies: ['LangChain', 'RAG', 'NLP', 'STT', 'TTS', 'Multilingual NLP'],
            icon: 'fas fa-robot',
            demoUrl: '#contact',
            githubUrl: '#contact'
        },
        {
            title: 'Olive Disease Classification',
            description: 'Multi-model AI system for precise olive disease detection using visual and contextual data.',
            features: [
                'ViT-based model for disease detection',
                'Fine-tuned vision model on labeled images',
                'Hybrid model combining vision and LLM',
                'Decision-making with contextual data'
            ],
            technologies: ['Vision Transformer', 'YOLOv8', 'LLaMA 3.2', 'ChatGPT', 'Computer Vision'],
            icon: 'fas fa-leaf',
            demoUrl: '#contact',
            githubUrl: '#contact'
        },
        {
            title: 'Field Boundary Detection',
            description: 'AI-powered satellite imagery analysis for automatic field boundary identification.',
            features: [
                'Advanced models for boundary detection',
                'Automated field identification process',
                'Enhanced farm management efficiency',
                'Reduced human error in boundary definition'
            ],
            technologies: ['Satellite Imagery', 'Deep Learning', 'Image Segmentation', 'Remote Sensing'],
            icon: 'fas fa-satellite',
            demoUrl: '#contact',
            githubUrl: '#contact'
        },
        {
            title: 'Phishing Email Detection',
            description: 'AI-powered system for detecting and preventing phishing email attacks.',
            features: [
                'Advanced NLP for email content analysis',
                'URL and attachment security analysis',
                'Gmail API integration',
                'Real-time threat detection'
            ],
            technologies: ['NLP', 'Django', 'Python', 'Gmail API', 'Machine Learning'],
            icon: 'fas fa-shield-alt',
            demoUrl: '#contact',
            githubUrl: '#contact'
        }
    ];

    projects.forEach((project, index) => {
        const projectCard = document.createElement('div');
        projectCard.className = 'project-card';
        projectCard.setAttribute('data-aos', 'fade-up');
        projectCard.setAttribute('data-aos-delay', (index * 150).toString());

        projectCard.innerHTML = `
            <div class="project-header">
                <div class="project-icon">
                    <i class="${project.icon}"></i>
                </div>
                <h3 class="project-title">${project.title}</h3>
                <p class="project-description">${project.description}</p>
            </div>
            <div class="project-content">
                <div class="project-features-section">
                    <h4>Key Features</h4>
                    <ul class="project-features">
                        ${project.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                </div>
                <div class="project-tech-section">
                    <h4>Technologies</h4>
                    <div class="project-tech">
                        ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                    </div>
                </div>
                <div class="project-links">
                    <div class="project-link primary">
                        <i class="fas fa-eye"></i>
                        <span>View Details</span>
                    </div>
                    <div class="project-link secondary">
                        <i class="fab fa-github"></i>
                        <span>Source Code</span>
                    </div>
                </div>
            </div>
        `;

        // Enhanced click handler for the entire card
        projectCard.addEventListener('click', (e) => {
            e.preventDefault();

            // Add visual feedback
            projectCard.style.transform = 'translateY(-8px) scale(1.01)';
            setTimeout(() => {
                projectCard.style.transform = '';
            }, 200);

            // Navigate to contact section
            const contactSection = document.getElementById('contact');
            if (contactSection) {
                const navbarHeight = document.getElementById('navbar').offsetHeight;
                const targetPosition = contactSection.offsetTop - navbarHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Show enhanced notification
                setTimeout(() => {
                    const notification = document.createElement('div');
                    notification.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: var(--bg-card);
                        color: var(--text-primary);
                        padding: 2rem;
                        border-radius: 1rem;
                        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                        z-index: 10000;
                        text-align: center;
                        border: 2px solid var(--primary-color);
                        max-width: 400px;
                        animation: fadeInScale 0.3s ease-out;
                    `;

                    notification.innerHTML = `
                        <h3 style="color: var(--primary-color); margin-bottom: 1rem;">${project.title}</h3>
                        <p style="margin-bottom: 1.5rem; color: var(--text-secondary);">Thank you for your interest! Please contact me to learn more about this project.</p>
                        <button onclick="this.parentElement.remove()" style="
                            background: var(--gradient);
                            color: white;
                            border: none;
                            padding: 0.75rem 1.5rem;
                            border-radius: 0.5rem;
                            cursor: pointer;
                            font-weight: 600;
                        ">Got it!</button>
                    `;

                    document.body.appendChild(notification);

                    // Auto-remove after 5 seconds
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 5000);
                }, 800);
            }
        });

        // Add hover sound effect (optional)
        projectCard.addEventListener('mouseenter', () => {
            projectCard.style.cursor = 'pointer';
        });

        projectsGrid.appendChild(projectCard);
    });

    // Reinitialize AOS for new elements
    AOS.refresh();
}

// Add skills to the skills section
function loadSkills() {
    const skillsContent = document.querySelector('.skills-content');

    const skillsData = {
        'Technical Skills': {
            icon: 'fas fa-code',
            skills: [
                { name: 'Python', level: 95 },
                { name: 'Machine Learning', level: 90 },
                { name: 'Deep Learning', level: 88 },
                { name: 'TensorFlow/Keras', level: 85 },
                { name: 'PyTorch', level: 82 },
                { name: 'NLP', level: 87 }
            ]
        },
        'Data & Analytics': {
            icon: 'fas fa-chart-bar',
            skills: [
                { name: 'Data Analysis', level: 92 },
                { name: 'Data Preprocessing', level: 90 },
                { name: 'Computer Vision', level: 85 },
                { name: 'Time Series Analysis', level: 80 },
                { name: 'Statistical Modeling', level: 83 },
                { name: 'Data Visualization', level: 88 }
            ]
        }
    };

    // Create skills categories
    const skillsLeft = document.createElement('div');
    skillsLeft.className = 'skills-left';

    Object.entries(skillsData).forEach(([category, data]) => {
        const skillsCategory = document.createElement('div');
        skillsCategory.className = 'skills-category';
        skillsCategory.setAttribute('data-aos', 'fade-up');

        skillsCategory.innerHTML = `
            <h3><i class="${data.icon}"></i> ${category}</h3>
            ${data.skills.map(skill => `
                <div class="skill-item">
                    <div class="skill-header">
                        <span class="skill-name">${skill.name}</span>
                        <span class="skill-level">${skill.level}%</span>
                    </div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: ${skill.level}%"></div>
                    </div>
                </div>
            `).join('')}
        `;

        skillsLeft.appendChild(skillsCategory);
    });

    // Create chart container
    const skillsRight = document.createElement('div');
    skillsRight.className = 'skills-chart';
    skillsRight.setAttribute('data-aos', 'fade-left');
    skillsRight.innerHTML = `
        <div class="chart-container">
            <h3><i class="fas fa-chart-radar"></i> Skills Overview</h3>
            <canvas id="skillsChart" width="400" height="400"></canvas>
        </div>
    `;

    skillsContent.appendChild(skillsLeft);
    skillsContent.appendChild(skillsRight);

    // Create the chart after a short delay to ensure canvas is rendered
    setTimeout(createSkillsChart, 100);
}

// Add contact information
function loadContact() {
    const contactContent = document.querySelector('.contact-content');
    
    contactContent.innerHTML = `
        <div class="contact-info" data-aos="fade-right">
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="contact-details">
                    <h4>Email</h4>
                    <p><EMAIL></p>
                </div>
            </div>
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <div class="contact-details">
                    <h4>Phone</h4>
                    <p>+216 99 20 20 76</p>
                </div>
            </div>
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="contact-details">
                    <h4>Location</h4>
                    <p>Nabeul, Tunisia</p>
                </div>
            </div>
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fab fa-linkedin"></i>
                </div>
                <div class="contact-details">
                    <h4>LinkedIn</h4>
                    <p>linkedin.com/in/kalil-dimassi</p>
                </div>
            </div>
        </div>
        <div class="contact-form" data-aos="fade-left">
            <form id="contact-form">
                <div class="form-group">
                    <label for="name">Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="subject">Subject</label>
                    <input type="text" id="subject" name="subject" required>
                </div>
                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Send Message
                </button>
            </form>
        </div>
    `;
}

// Enhanced cursor effects
function initCursorEffects() {
    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    document.body.appendChild(cursor);

    const cursorFollower = document.createElement('div');
    cursorFollower.className = 'cursor-follower';
    document.body.appendChild(cursorFollower);

    let mouseX = 0, mouseY = 0;
    let followerX = 0, followerY = 0;

    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;

        cursor.style.left = mouseX + 'px';
        cursor.style.top = mouseY + 'px';
    });

    function animateFollower() {
        followerX += (mouseX - followerX) * 0.1;
        followerY += (mouseY - followerY) * 0.1;

        cursorFollower.style.left = followerX + 'px';
        cursorFollower.style.top = followerY + 'px';

        requestAnimationFrame(animateFollower);
    }
    animateFollower();

    // Add hover effects
    document.querySelectorAll('a, button, .project-card, .skill-item').forEach(el => {
        el.addEventListener('mouseenter', () => {
            cursor.classList.add('cursor-hover');
            cursorFollower.classList.add('cursor-hover');
        });

        el.addEventListener('mouseleave', () => {
            cursor.classList.remove('cursor-hover');
            cursorFollower.classList.remove('cursor-hover');
        });
    });
}

// Enhanced scroll effects
function initScrollEffects() {
    let lastScrollTop = 0;
    const navbar = document.getElementById('navbar');

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Navbar hide/show on scroll
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }
        lastScrollTop = scrollTop;

        // Parallax effect for hero background
        const hero = document.querySelector('.hero');
        if (hero) {
            const scrolled = window.pageYOffset;
            const parallax = scrolled * 0.5;
            hero.style.transform = `translateY(${parallax}px)`;
        }
    });
}

// Loading Screen
function initLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    const loadingProgress = document.querySelector('.loading-progress');

    // Simulate loading progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);

            // Hide loading screen after a short delay
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                document.body.style.overflow = 'auto';

                // Initialize animations after loading
                setTimeout(() => {
                    initGSAPAnimations();
                }, 300);
            }, 500);
        }
        loadingProgress.style.width = progress + '%';
    }, 100);
}

// Initialize content when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Hide body overflow during loading
    document.body.style.overflow = 'hidden';

    // Initialize loading screen
    initLoadingScreen();

    // Initialize other components
    initParticles();
    initCursorEffects();
    initScrollEffects();
    initScrollIndicator();

    // Load content with error handling
    try {
        loadProjects();
        loadSkills();
        loadContact();
    } catch (error) {
        console.error('Error loading content:', error);
    }

    // Enhanced skill bars animation
    const observerOptions = {
        threshold: 0.3,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const skillBars = entry.target.querySelectorAll('.skill-progress');
                skillBars.forEach((bar, index) => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                        bar.style.animation = 'skillGlow 0.5s ease-out';
                    }, index * 100);
                });
            }
        });
    }, observerOptions);

    document.querySelectorAll('.skills-category').forEach(category => {
        observer.observe(category);
    });

    // Add click effects to buttons
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// Contact form submission
document.addEventListener('submit', (e) => {
    if (e.target.id === 'contact-form') {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        
        // Simulate form submission
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            alert('Thank you for your message! I will get back to you soon.');
            e.target.reset();
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }
});

// Add some interactive data visualization
function createSkillsChart() {
    const ctx = document.getElementById('skillsChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['Python', 'Machine Learning', 'Deep Learning', 'NLP', 'Computer Vision', 'Data Analysis'],
                datasets: [{
                    label: 'Skill Level',
                    data: [95, 90, 88, 87, 85, 92],
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }
}
