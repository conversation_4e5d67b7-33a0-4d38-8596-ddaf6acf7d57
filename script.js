// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});

// Theme Toggle Functionality
const themeToggle = document.getElementById('theme-toggle');
const body = document.body;

// Check for saved theme preference or default to light mode
const currentTheme = localStorage.getItem('theme') || 'light';
if (currentTheme === 'dark') {
    body.setAttribute('data-theme', 'dark');
    themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
}

themeToggle.addEventListener('click', () => {
    const currentTheme = body.getAttribute('data-theme');
    if (currentTheme === 'dark') {
        body.removeAttribute('data-theme');
        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        localStorage.setItem('theme', 'light');
    } else {
        body.setAttribute('data-theme', 'dark');
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        localStorage.setItem('theme', 'dark');
    }
});

// Mobile Navigation Toggle
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');

navToggle.addEventListener('click', () => {
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('active');
    });
});

// Typing Animation
const typingText = document.getElementById('typing-text');
const texts = [
    'Data Scientist Engineer',
    'Machine Learning Expert',
    'Deep Learning Specialist',
    'AI Solutions Developer',
    'NLP Enthusiast'
];

let textIndex = 0;
let charIndex = 0;
let isDeleting = false;

function typeWriter() {
    const currentText = texts[textIndex];
    
    if (isDeleting) {
        typingText.textContent = currentText.substring(0, charIndex - 1);
        charIndex--;
    } else {
        typingText.textContent = currentText.substring(0, charIndex + 1);
        charIndex++;
    }
    
    let typeSpeed = isDeleting ? 50 : 100;
    
    if (!isDeleting && charIndex === currentText.length) {
        typeSpeed = 2000; // Pause at end
        isDeleting = true;
    } else if (isDeleting && charIndex === 0) {
        isDeleting = false;
        textIndex = (textIndex + 1) % texts.length;
        typeSpeed = 500; // Pause before next text
    }
    
    setTimeout(typeWriter, typeSpeed);
}

// Start typing animation
typeWriter();

// Navbar scroll effect
window.addEventListener('scroll', () => {
    const navbar = document.getElementById('navbar');
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        if (body.getAttribute('data-theme') === 'dark') {
            navbar.style.background = 'rgba(17, 24, 39, 0.98)';
        }
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        if (body.getAttribute('data-theme') === 'dark') {
            navbar.style.background = 'rgba(17, 24, 39, 0.95)';
        }
    }
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Active navigation link highlighting
window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Add projects to the projects section
function loadProjects() {
    const projectsGrid = document.querySelector('.projects-grid');
    
    const projects = [
        {
            title: 'Generative AI with LangChain',
            description: 'Advanced Generative AI system with multilingual support and RAG architecture for data interaction.',
            features: [
                'Built RAG architecture with LangChain',
                'Natural language understanding for Q&A',
                'Multilingual speech recognition and synthesis',
                'Support for Tunisian dialect'
            ],
            technologies: ['LangChain', 'RAG', 'NLP', 'STT', 'TTS', 'Multilingual NLP'],
            icon: 'fas fa-robot'
        },
        {
            title: 'Olive Disease Classification',
            description: 'Multi-model AI system for precise olive disease detection using visual and contextual data.',
            features: [
                'ViT-based model for disease detection',
                'Fine-tuned vision model on labeled images',
                'Hybrid model combining vision and LLM',
                'Decision-making with contextual data'
            ],
            technologies: ['Vision Transformer', 'YOLOv8', 'LLaMA 3.2', 'ChatGPT', 'Computer Vision'],
            icon: 'fas fa-leaf'
        },
        {
            title: 'Field Boundary Detection',
            description: 'AI-powered satellite imagery analysis for automatic field boundary identification.',
            features: [
                'Advanced models for boundary detection',
                'Automated field identification process',
                'Enhanced farm management efficiency',
                'Reduced human error in boundary definition'
            ],
            technologies: ['Satellite Imagery', 'Deep Learning', 'Image Segmentation', 'Remote Sensing'],
            icon: 'fas fa-satellite'
        },
        {
            title: 'Phishing Email Detection',
            description: 'AI-powered system for detecting and preventing phishing email attacks.',
            features: [
                'Advanced NLP for email content analysis',
                'URL and attachment security analysis',
                'Gmail API integration',
                'Real-time threat detection'
            ],
            technologies: ['NLP', 'Django', 'Python', 'Gmail API', 'Machine Learning'],
            icon: 'fas fa-shield-alt'
        }
    ];
    
    projects.forEach(project => {
        const projectCard = document.createElement('div');
        projectCard.className = 'project-card';
        projectCard.setAttribute('data-aos', 'fade-up');
        
        projectCard.innerHTML = `
            <div class="project-header">
                <div class="project-icon">
                    <i class="${project.icon}"></i>
                </div>
                <h3 class="project-title">${project.title}</h3>
                <p class="project-description">${project.description}</p>
            </div>
            <div class="project-content">
                <ul class="project-features">
                    ${project.features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
                <div class="project-tech">
                    ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                </div>
                <div class="project-links">
                    <a href="#" class="project-link primary">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    <a href="#" class="project-link secondary">
                        <i class="fab fa-github"></i> Source Code
                    </a>
                </div>
            </div>
        `;
        
        projectsGrid.appendChild(projectCard);
    });
}

// Add skills to the skills section
function loadSkills() {
    const skillsContent = document.querySelector('.skills-content');

    const skillsData = {
        'Technical Skills': {
            icon: 'fas fa-code',
            skills: [
                { name: 'Python', level: 95 },
                { name: 'Machine Learning', level: 90 },
                { name: 'Deep Learning', level: 88 },
                { name: 'TensorFlow/Keras', level: 85 },
                { name: 'PyTorch', level: 82 },
                { name: 'NLP', level: 87 }
            ]
        },
        'Data & Analytics': {
            icon: 'fas fa-chart-bar',
            skills: [
                { name: 'Data Analysis', level: 92 },
                { name: 'Data Preprocessing', level: 90 },
                { name: 'Computer Vision', level: 85 },
                { name: 'Time Series Analysis', level: 80 },
                { name: 'Statistical Modeling', level: 83 },
                { name: 'Data Visualization', level: 88 }
            ]
        }
    };

    // Create skills categories
    const skillsLeft = document.createElement('div');
    skillsLeft.className = 'skills-left';

    Object.entries(skillsData).forEach(([category, data]) => {
        const skillsCategory = document.createElement('div');
        skillsCategory.className = 'skills-category';
        skillsCategory.setAttribute('data-aos', 'fade-up');

        skillsCategory.innerHTML = `
            <h3><i class="${data.icon}"></i> ${category}</h3>
            ${data.skills.map(skill => `
                <div class="skill-item">
                    <div class="skill-header">
                        <span class="skill-name">${skill.name}</span>
                        <span class="skill-level">${skill.level}%</span>
                    </div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: ${skill.level}%"></div>
                    </div>
                </div>
            `).join('')}
        `;

        skillsLeft.appendChild(skillsCategory);
    });

    // Create chart container
    const skillsRight = document.createElement('div');
    skillsRight.className = 'skills-chart';
    skillsRight.setAttribute('data-aos', 'fade-left');
    skillsRight.innerHTML = `
        <div class="chart-container">
            <h3><i class="fas fa-chart-radar"></i> Skills Overview</h3>
            <canvas id="skillsChart" width="400" height="400"></canvas>
        </div>
    `;

    skillsContent.appendChild(skillsLeft);
    skillsContent.appendChild(skillsRight);

    // Create the chart after a short delay to ensure canvas is rendered
    setTimeout(createSkillsChart, 100);
}

// Add contact information
function loadContact() {
    const contactContent = document.querySelector('.contact-content');
    
    contactContent.innerHTML = `
        <div class="contact-info" data-aos="fade-right">
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="contact-details">
                    <h4>Email</h4>
                    <p><EMAIL></p>
                </div>
            </div>
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <div class="contact-details">
                    <h4>Phone</h4>
                    <p>+216 99 20 20 76</p>
                </div>
            </div>
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="contact-details">
                    <h4>Location</h4>
                    <p>Nabeul, Tunisia</p>
                </div>
            </div>
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fab fa-linkedin"></i>
                </div>
                <div class="contact-details">
                    <h4>LinkedIn</h4>
                    <p>linkedin.com/in/kalil-dimassi</p>
                </div>
            </div>
        </div>
        <div class="contact-form" data-aos="fade-left">
            <form id="contact-form">
                <div class="form-group">
                    <label for="name">Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="subject">Subject</label>
                    <input type="text" id="subject" name="subject" required>
                </div>
                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Send Message
                </button>
            </form>
        </div>
    `;
}

// Initialize content when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    loadProjects();
    loadSkills();
    loadContact();
    
    // Animate skill bars when they come into view
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const skillBars = entry.target.querySelectorAll('.skill-progress');
                skillBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.skills-category').forEach(category => {
        observer.observe(category);
    });
});

// Contact form submission
document.addEventListener('submit', (e) => {
    if (e.target.id === 'contact-form') {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        
        // Simulate form submission
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            alert('Thank you for your message! I will get back to you soon.');
            e.target.reset();
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }
});

// Add some interactive data visualization
function createSkillsChart() {
    const ctx = document.getElementById('skillsChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['Python', 'Machine Learning', 'Deep Learning', 'NLP', 'Computer Vision', 'Data Analysis'],
                datasets: [{
                    label: 'Skill Level',
                    data: [95, 90, 88, 87, 85, 92],
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }
}
