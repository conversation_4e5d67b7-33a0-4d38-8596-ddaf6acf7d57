# Kalil <PERSON> - Data Scientist Portfolio

A modern, interactive portfolio website showcasing the work and expertise of <PERSON><PERSON>, a Data Scientist Engineer specializing in Machine Learning, Deep Learning, NLP, and Computer Vision.

## 🚀 Features

- **Responsive Design**: Fully responsive layout that works on all devices
- **Dark/Light Theme**: Toggle between dark and light themes
- **Interactive Animations**: Smooth animations using AOS (Animate On Scroll)
- **Typing Animation**: Dynamic typing effect in the hero section
- **Skills Visualization**: Interactive skill bars and radar chart
- **Project Showcase**: Detailed project cards with technologies used
- **Contact Form**: Functional contact form with validation
- **Modern UI/UX**: Clean, professional design with smooth transitions

## 🛠️ Technologies Used

### Frontend
- **HTML5**: Semantic markup and structure
- **CSS3**: Modern styling with CSS Grid, Flexbox, and custom properties
- **JavaScript (ES6+)**: Interactive functionality and animations
- **Chart.js**: Data visualization for skills radar chart
- **AOS Library**: Animate On Scroll effects
- **Font Awesome**: Icons and visual elements
- **Google Fonts**: Inter font family

### Design Features
- CSS Custom Properties for theming
- CSS Grid and Flexbox for layout
- Smooth scrolling and transitions
- Mobile-first responsive design
- Accessibility considerations

## 📁 Project Structure

```
kalilPortfolio/
├── index.html          # Main HTML file
├── styles.css          # CSS styles and themes
├── script.js           # JavaScript functionality
├── README.md           # Project documentation
├── extract_cv.py       # CV text extraction utility
└── cv_text.txt         # Extracted CV content
```

## 🎨 Sections

### 1. Hero Section
- Professional introduction with typing animation
- Call-to-action buttons
- Floating technology icons
- Scroll indicator

### 2. About Section
- Personal background and expertise
- Professional statistics
- Quick facts card

### 3. Experience Section
- Interactive timeline of professional experience
- Detailed achievements and responsibilities
- Technology tags for each role

### 4. Projects Section
- Featured project cards with:
  - Project descriptions
  - Key features and achievements
  - Technology stacks used
  - Links to demos and source code

### 5. Skills Section
- Interactive skill bars showing proficiency levels
- Radar chart visualization
- Categorized technical skills

### 6. Education & Certifications
- Academic background
- Professional certifications from NVIDIA and AWS
- Institution details and achievements

### 7. Contact Section
- Contact information cards
- Interactive contact form
- Social media links

## 🚀 Getting Started

1. **Clone or download** the repository
2. **Open** `index.html` in a web browser
3. **Customize** the content in the HTML, CSS, and JavaScript files as needed

### Local Development

For local development with live reload, you can use a simple HTTP server:

```bash
# Using Python
python -m http.server 8000

# Using Node.js (if you have http-server installed)
npx http-server

# Using PHP
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

## 🎯 Customization

### Updating Personal Information
1. Edit the content in `index.html`
2. Update contact information and social links
3. Modify project details and achievements

### Changing Colors and Themes
1. Update CSS custom properties in `:root` and `[data-theme="dark"]`
2. Modify gradient colors and accent colors
3. Adjust shadow and border styles

### Adding New Projects
1. Update the `projects` array in `script.js`
2. Add project details, features, and technologies
3. Include appropriate icons and links

### Modifying Skills
1. Update the `skillsData` object in `script.js`
2. Adjust skill levels and categories
3. Modify the radar chart data in `createSkillsChart()`

## 📱 Responsive Design

The portfolio is fully responsive and optimized for:
- **Desktop**: Full layout with all features
- **Tablet**: Adapted grid layouts and navigation
- **Mobile**: Stacked layouts and mobile-friendly navigation

## 🌟 Key Features Explained

### Theme Toggle
- Persistent theme selection using localStorage
- Smooth transitions between themes
- Automatic icon updates

### Typing Animation
- Multiple rotating text phrases
- Realistic typing and deleting effects
- Customizable speed and phrases

### Skill Visualization
- Animated progress bars
- Interactive radar chart using Chart.js
- Intersection Observer for scroll-triggered animations

### Contact Form
- Form validation and submission handling
- Loading states and user feedback
- Responsive form layout

## 🔧 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👤 About Kalil Dimassi

Data Scientist Engineer with expertise in:
- Machine Learning and Deep Learning
- Natural Language Processing (NLP)
- Computer Vision
- Generative AI and LangChain
- Data Analysis and Visualization

**Contact Information:**
- Email: <EMAIL>
- Phone: +216 99 20 20 76
- LinkedIn: [linkedin.com/in/kalil-dimassi-965515228](https://linkedin.com/in/kalil-dimassi-965515228)
- Location: Nabeul, Tunisia

---

*Built with ❤️ by Kalil Dimassi*
