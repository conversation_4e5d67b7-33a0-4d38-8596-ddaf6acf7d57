Kalil Dimassi  
Email: <EMAIL> | linkedin.com/in/kalil -dimassi  | 
Phone : +216 99 20 20 76  | Nabeul, Tunisia  
 
PROFESSIONAL EXPERIENCE  
Data Scientist Engineer  
01/2024 – Present    SEABEX  MEA  / Tunis , Tunisia  
▪ Developed and deployed machine learning models to drive 
predictive analytics and optimize decision -making.  
▪ Extracted actionable insights through data analysis, improving 
business strategies and processes.  
▪ Enhanced model performance with continuous optimization and 
automation while building scalable pipelines for efficient data 
processing and real -time inference.  
▪ Fine -tuned deep learning models for various AI applications, 
optimizing accuracy and efficiency.  
▪ Integrated AI -driven solutions into business operations, 
streamlining workflows and enhancing automation.  
▪ Collaborated cross -functionally to integrate AI solutions, driving 
efficiency and innovation.  
EDUCATION  
09/20 19 – 12/2024                                    ESPRIT: Private Higher School of Engineering and  Technology  
• Engineering degree in Computer Science  
 
ACADEMIC PROJECT  
Generative AI Project with LangChain  
This project focused on developing a Generative AI system for data interaction and multilingual support.  
▪ Built a Generative AI system with LangChain, using tools and agents to create a Retrieval -Augmented 
Generation (RAG) architecture.  
▪ Created a system to answer users’ general and data -specific questions using natural language 
understanding.  
▪ Implemented a multilingual speech recognition and synthesis system that supports multiple languages, 
including Tunisian dialect.  
Tool s: LangChain, Retrieval -Augmented Generation (RAG), Natural Language Processing (NLP), Speech 
Recognition (STT), Text -to-Speech (TTS), Multilingual NLP .  
 
Olive Disease Classification System Development  
This project focused on developing a multi -model AI system for precise olive disease detection using visual and 
contextual data.  
▪ Developed a ViT -based model for olive disease detection with a small dataset.  
▪ Fine -tuned a vision model on labeled olive leaf images.  
▪ Built a hybrid model combining vision and LLM for accurate disease identification.  
▪ Created a decision -making model using outputs from all models and contextual data (weather, 
temperature).  
Tools: Vision Transformer (ViT), YOLOv8 (Classification), LLaMA 3.2 Vision, ChatGPT, Hybrid AI Architecture, 
Custom Image Dataset . 

--- Page Break ---

 
 
Field Boundary Detection  
This project uses AI and satellite imagery to automatically identify precise field boundaries, improving accuracy 
over traditional manual methods and enhancing farm management.  
▪ Applied advanced models for accurate field boundary detection from satellite data.  
▪ Automated the field boundary identification process, enhancing efficiency in farm management.  
▪ Reduced human error by streamlining the process of defining field boundaries.  
Tools: Satellite Imagery, Deep Learning, Image Segmentation, Remote Sensing, Computer Vision . 
 
Phishing Email Detection  
This project uses AI and satellite imagery to automatically identify precise field boundaries, improving accuracy 
over traditional manual methods and enhancing farm management.  
▪ Applied advanced models for accurate field boundary detection from satellite data.  
▪ Automated the field boundary identification process, enhancing efficiency in farm management.  
▪ Reduced human error by streamlining the process of defining field boundaries.  
Tools: Natural Language Processing (NLP), Django, Python, Gmail API, Machine Learning (Classification), URL 
& Attachment Analysis.  
 
SKILLS  
• Data analysis, preprocessing, ML model development.  
• Deep learning: TensorFlow, Keras, PyTorch.  
• NLP , computer vision, time -series analysis.  
• RAG, LangChain, LLMs, Transformer models , Ollama, MCP .  
• API development, data visualization (Matplotlib, Power BI).  
• Frameworks: .NET, SpringBoot, Symfony, Django, Flask.  
• Git, GitHub, GitLab, Jenkins, Docker, Vagrant.   
 
CERTIFICAT ES 
NVIDIA Deep Learning Institute Certificate  
     Nvidia: Building Transformer -Based NLP Applications  
     Nvidia: Application of AI Anomaly Detection   
AWS Cloud Practitioner  
 
LANGUAGES  
 
 Arabic : Native                     English : Intermediate                     Spanish : Beginner  

--- Page Break ---

