/* CSS Variables for Theme - Optimized for Eye Comfort */
:root {
    --primary-color: #3b82f6;
    --secondary-color: #1e40af;
    --accent-color: #f59e0b;
    /* Light Mode - Softer, warmer text colors for reduced eye strain */
    --text-primary: #2d3748;        /* Soft dark gray instead of pure black */
    --text-secondary: #4a5568;      /* Medium gray for descriptions */
    --text-muted: #718096;          /* Light gray for less important text */
    --text-accent: #2b6cb0;         /* Softer blue for accents */
    --bg-primary: #fefefe;          /* Very soft white */
    --bg-secondary: #f7fafc;        /* Light blue-gray background */
    --bg-card: #ffffff;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

[data-theme="dark"] {
    /* Dark Mode - Gentle, muted light colors that aren't harsh on eyes */
    --text-primary: #e2e8f0;        /* Soft white instead of pure white */
    --text-secondary: #a0aec0;      /* Muted light gray for descriptions */
    --text-muted: #718096;          /* Consistent muted tone */
    --text-accent: #63b3ed;         /* Softer blue for dark mode */
    --bg-primary: #0f172a;          /* Deep blue-black for reduced eye strain */
    --bg-secondary: #1e293b;        /* Slightly lighter blue-gray */
    --bg-card: #334155;             /* Medium blue-gray for cards */
    --border-color: #475569;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: all 0.3s ease;
    cursor: none;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    animation: loadingPulse 2s ease-in-out infinite;
}

.loading-logo {
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.loading-profile-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(59, 130, 246, 0.3);
    animation: logoGlow 2s ease-in-out infinite;
}

.loading-profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.loading-logo .logo-text {
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: logoGlow 2s ease-in-out infinite;
}

.loading-text {
    margin-bottom: 2rem;
    font-size: 1.5rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.loading-dots span {
    animation: dotBounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

.loading-bar {
    width: 200px;
    height: 4px;
    background: var(--bg-secondary);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    height: 100%;
    background: var(--gradient);
    border-radius: 2px;
    animation: loadingProgress 2s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes logoGlow {
    0%, 100% {
        filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.5));
    }
    50% {
        filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.8));
    }
}

@keyframes dotBounce {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes loadingProgress {
    0% {
        width: 0%;
        transform: translateX(-100%);
    }
    50% {
        width: 100%;
        transform: translateX(0%);
    }
    100% {
        width: 100%;
        transform: translateX(100%);
    }
}

/* Custom Cursor */
.custom-cursor {
    position: fixed;
    width: 10px;
    height: 10px;
    background: var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    mix-blend-mode: difference;
}

.cursor-follower {
    position: fixed;
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    opacity: 0.5;
    transition: transform 0.3s ease;
}

.custom-cursor.cursor-hover {
    transform: scale(2);
}

.cursor-follower.cursor-hover {
    transform: scale(1.5);
    opacity: 0.8;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

@keyframes rippleEffect {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Skill Glow Animation */
@keyframes skillGlow {
    0% {
        box-shadow: 0 0 5px var(--primary-color);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
    }
    100% {
        box-shadow: 0 0 5px var(--primary-color);
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .navbar {
    background: rgba(17, 24, 39, 0.95);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background: var(--bg-secondary);
    transform: scale(1.1);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--bg-primary);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(30, 64, 175, 0.05));
    z-index: 1;
}

/* Particle Background */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

/* Animated Background Shapes */
.bg-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
    animation: floatShape 20s infinite linear;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: -5s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    top: 80%;
    left: 20%;
    animation-delay: -10s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    top: 10%;
    right: 30%;
    animation-delay: -15s;
}

@keyframes floatShape {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-40px) rotate(180deg);
        opacity: 0.3;
    }
    75% {
        transform: translateY(-20px) rotate(270deg);
        opacity: 0.6;
    }
}

/* Hero Badge */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
    animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
}

/* Glitch Effect for Name */
.glitch {
    position: relative;
    animation: glitch 3s infinite;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
}

.glitch::before {
    animation: glitch-1 0.5s infinite;
    color: #ff0000;
    z-index: -1;
}

.glitch::after {
    animation: glitch-2 0.5s infinite;
    color: #00ff00;
    z-index: -2;
}

@keyframes glitch {
    0%, 90%, 100% {
        transform: translate(0);
    }
    10% {
        transform: translate(-2px, 2px);
    }
    20% {
        transform: translate(2px, -2px);
    }
    30% {
        transform: translate(-2px, 2px);
    }
    40% {
        transform: translate(2px, -2px);
    }
    50% {
        transform: translate(-2px, 2px);
    }
    60% {
        transform: translate(2px, -2px);
    }
    70% {
        transform: translate(-2px, 2px);
    }
    80% {
        transform: translate(2px, -2px);
    }
}

@keyframes glitch-1 {
    0%, 90%, 100% {
        opacity: 0;
        transform: translate(0);
    }
    10% {
        opacity: 0.3;
        transform: translate(-2px, 2px);
    }
}

@keyframes glitch-2 {
    0%, 90%, 100% {
        opacity: 0;
        transform: translate(0);
    }
    20% {
        opacity: 0.3;
        transform: translate(2px, -2px);
    }
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    width: 100%;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
}

.highlight {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
}

.typing-text {
    color: var(--primary-color);
    font-weight: 600;
}

.cursor {
    animation: blink 1s infinite;
    margin-left: 2px;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    line-height: 1.7;
    max-width: 90%;
}

.about-text p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-secondary);
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1rem;
    margin-bottom: 0;
}

.project-features li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.btn-animated {
    background-size: 200% 100%;
    background-position: 100% 0;
    transition: all 0.3s ease;
}

.btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-animated:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient);
    color: white;
    border: none;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-lg);
    background-position: 0% 0;
}

.btn-primary i {
    transition: transform 0.3s ease;
}

.btn-primary:hover i {
    transform: translateX(5px);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    position: relative;
}

.btn-secondary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
    z-index: -1;
}

.btn-secondary:hover::after {
    width: 100%;
}

.btn-secondary:hover {
    color: white;
    transform: translateY(-3px) scale(1.05);
    border-color: var(--primary-color);
}

.btn-secondary i {
    transition: transform 0.3s ease;
}

.btn-secondary:hover i {
    transform: scale(1.2);
}

/* Hero Social Links */
.hero-social {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.social-icon {
    width: 50px;
    height: 50px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.social-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient);
    transform: scale(0);
    transition: transform 0.3s ease;
    border-radius: 50%;
}

.social-icon:hover::before {
    transform: scale(1);
}

.social-icon:hover {
    color: white;
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
}

.social-icon i {
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.social-icon:hover i {
    transform: scale(1.2);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}

.image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.profile-image {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
    background: transparent;
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.profile-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.profile-image:hover::before {
    opacity: 1;
}

.profile-image.pulse {
    animation: imagePulse 3s ease-in-out infinite;
}

.profile-image:hover img {
    transform: scale(1.05);
}

@keyframes imagePulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 25px 50px rgba(59, 130, 246, 0.5);
    }
}

.image-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: conic-gradient(from 0deg, #3b82f6, #8b5cf6, #06b6d4, #10b981, #f59e0b, #ef4444, #3b82f6);
    border-radius: 50%;
    z-index: -1;
    animation: rotateGlow 4s linear infinite;
    opacity: 0.7;
    filter: blur(20px);
}

@keyframes rotateGlow {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-icon {
    position: absolute;
    width: 70px;
    height: 70px;
    background: var(--bg-card);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    animation: floatComplex 6s ease-in-out infinite;
    animation-delay: var(--delay);
    border: 2px solid rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
}

.tech-icon {
    position: relative;
}

.tech-icon::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    z-index: 10;
}

.tech-icon::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid rgba(0, 0, 0, 0.8);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10;
}

.tech-icon:hover::before,
.tech-icon:hover::after {
    opacity: 1;
}

.tech-icon:hover {
    transform: scale(1.2) translateY(-10px);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.4);
    background: var(--gradient);
    color: white;
}

.floating-icon:nth-child(1) {
    top: 10%;
    right: 5%;
}

.floating-icon:nth-child(2) {
    bottom: 40%;
    left: 5%;
}

.floating-icon:nth-child(3) {
    top: 50%;
    right: 15%;
}

.floating-icon:nth-child(4) {
    top: 75%;
    right: 35%;
}

.floating-icon:nth-child(5) {
    bottom: 15%;
    left: 25%;
}

.floating-icon:nth-child(6) {
    top: 25%;
    left: 15%;
}

@keyframes floatComplex {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-15px) rotate(5deg);
    }
    50% {
        transform: translateY(-30px) rotate(0deg);
    }
    75% {
        transform: translateY(-15px) rotate(-5deg);
    }
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    animation: scrollFloat 3s ease-in-out infinite;
    cursor: pointer;
    z-index: 3;
    padding: 1rem;
    border-radius: 1rem;
    transition: all 0.3s ease;
}

.scroll-indicator:hover {
    background: rgba(59, 130, 246, 0.1);
    transform: translateX(-50%) scale(1.1);
}

.scroll-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.8;
    animation: fadeInOut 2s ease-in-out infinite;
}

.scroll-arrow {
    color: var(--primary-color);
    font-size: 1.5rem;
    animation: arrowBounce 2s ease-in-out infinite;
}

.scroll-line {
    width: 2px;
    height: 30px;
    background: linear-gradient(to bottom, var(--primary-color), transparent);
    animation: lineGrow 2s ease-in-out infinite;
}

@keyframes scrollFloat {
    0%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    50% {
        transform: translateX(-50%) translateY(-10px);
    }
}

@keyframes arrowBounce {
    0%, 100% {
        transform: translateY(0);
        opacity: 1;
    }
    50% {
        transform: translateY(10px);
        opacity: 0.6;
    }
}

@keyframes fadeInOut {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 0.4;
    }
}

@keyframes lineGrow {
    0%, 100% {
        height: 20px;
        opacity: 0.6;
    }
    50% {
        height: 40px;
        opacity: 1;
    }
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* About Section */
.about {
    background: var(--bg-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-secondary);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.about-card {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.about-card h3 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.facts-list {
    list-style: none;
}

.facts-list li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-secondary);
}

.facts-list i {
    color: var(--primary-color);
    width: 20px;
}

/* Experience Section */
.experience {
    background: var(--bg-primary);
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 100%;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
}

.timeline-marker {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    border: 4px solid var(--bg-primary);
    z-index: 2;
}

.timeline-content {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    margin-left: 60%;
    width: 40%;
}

.timeline-header h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.company {
    font-weight: 600;
    color: var(--text-primary);
    display: block;
    margin-bottom: 0.25rem;
}

.period {
    color: var(--accent-color);
    font-weight: 500;
    display: block;
    margin-bottom: 0.25rem;
}

.location {
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: block;
    margin-bottom: 1rem;
}

.timeline-achievements {
    list-style: none;
    margin-bottom: 1.5rem;
}

.timeline-achievements li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
}

.location {
    color: var(--text-muted);
    font-size: 0.9rem;
    display: block;
    margin-bottom: 1rem;
}

.timeline-achievements li::before {
    content: '▸';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.timeline-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    padding: 0.4rem 0.8rem;
    border-radius: 1.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tech-tag:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Projects Section */
.projects {
    background: var(--bg-secondary);
}

/* Enhanced Projects Section Layout */
.projects {
    background: var(--bg-secondary);
    padding: 5rem 0;
}

.projects .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
    margin-top: 3rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    justify-items: center;
}

/* Responsive Design for Projects */
@media (max-width: 1024px) {
    .projects-grid {
        max-width: 800px;
        gap: 2.5rem;
    }
}

@media (max-width: 768px) {
    .projects-grid {
        grid-template-columns: 1fr;
        max-width: 100%;
        gap: 2rem;
    }

    .projects {
        padding: 4rem 0;
    }
}

/* Enhanced Clickable Project Cards */
.project-card {
    background: var(--bg-card);
    border-radius: 1.2rem;
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    display: block;
    width: 100%;
    max-width: 450px;
    min-height: 500px;
    transform-origin: center;
}

/* Enhanced Project Card Interactions */
.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(147, 51, 234, 0.08));
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    border-radius: 1.2rem;
}

.project-card:hover::before {
    opacity: 1;
}

.project-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 30px 60px rgba(59, 130, 246, 0.25);
    border-color: rgba(59, 130, 246, 0.4);
}

.project-card:active {
    transform: translateY(-8px) scale(1.01);
    transition: all 0.2s ease;
}

.project-card:hover .project-icon {
    transform: scale(1.15) rotate(8deg);
    background: var(--gradient);
    color: white;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.project-card:hover .project-title {
    color: var(--primary-color);
    transform: translateX(5px);
}

.project-card:hover .project-description {
    color: var(--text-primary);
}

.project-card:hover .tech-tag {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);
}

/* Smooth transitions for all card elements */
.project-title,
.project-description,
.project-icon {
    transition: all 0.3s ease;
}

/* Notification Animation */
@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Enhanced Project Card Header */
.project-header {
    padding: 2rem 1.5rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    z-index: 2;
}

.project-icon {
    width: 75px;
    height: 75px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.project-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.project-card:hover .project-icon::before {
    transform: translateX(100%);
}

.project-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1rem;
    margin-bottom: 0;
}

/* Enhanced Project Content */
.project-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    position: relative;
    z-index: 2;
    flex-grow: 1;
}

.project-features-section,
.project-tech-section {
    margin-bottom: 1rem;
}

.project-features-section h4,
.project-tech-section h4 {
    color: var(--text-accent);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.project-card:hover .project-features-section h4,
.project-card:hover .project-tech-section h4 {
    color: var(--primary-color);
}

.project-features-section h4::before {
    content: '🎯';
    font-size: 1rem;
}

.project-tech-section h4::before {
    content: '🛠️';
    font-size: 1rem;
}

.project-features {
    list-style: none;
    margin: 0;
}

.project-features li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

.project-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
    font-size: 1.1rem;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 0;
}

/* Enhanced Project Links */
.project-links {
    display: flex;
    gap: 1rem;
    margin-top: auto;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.project-link {
    flex: 1;
    padding: 0.875rem 1rem;
    border-radius: 0.75rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
}

.project-link.primary {
    background: var(--gradient);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.project-link.secondary {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    border: 2px solid rgba(59, 130, 246, 0.3);
}

.project-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.project-card:hover .project-link::before {
    left: 100%;
}

.project-card:hover .project-link {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.project-card:hover .project-link.secondary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Skills Section */
.skills {
    background: var(--bg-primary);
}

.skills-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.skills-category {
    margin-bottom: 3rem;
}

.skills-category h3 {
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.skill-item {
    margin-bottom: 1.5rem;
}

.skill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.skill-name {
    font-weight: 500;
    color: var(--text-primary);
}

.skill-level {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.skill-bar {
    height: 10px;
    background: var(--bg-secondary);
    border-radius: 5px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.skill-progress {
    height: 100%;
    background: var(--gradient);
    border-radius: 5px;
    transition: width 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.skill-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.skill-item:hover .skill-progress {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    transform: scaleY(1.2);
}

.skill-item:hover .skill-name {
    color: var(--primary-color);
    transform: translateX(5px);
}

.skill-name,
.skill-level {
    transition: all 0.3s ease;
}

.skills-left {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.skills-chart {
    display: flex;
    justify-content: center;
    align-items: center;
}

.chart-container {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    text-align: center;
    width: 100%;
}

.chart-container h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

#skillsChart {
    max-width: 100%;
    height: auto;
}

/* Education Section */
.education {
    background: var(--bg-secondary);
}

.education-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.education-item {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    display: flex;
    gap: 1.5rem;
}

.education-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.education-details h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.institution {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.period {
    color: var(--accent-color);
    font-weight: 500;
    margin-bottom: 1rem;
}

.education-details .description {
    color: var(--text-secondary);
    line-height: 1.6;
}

.certifications {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.certifications h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.cert-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cert-item {
    display: flex;
    gap: 1rem;
    align-items: start;
}

.cert-icon {
    width: 50px;
    height: 50px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.cert-icon.nvidia {
    background: linear-gradient(135deg, #76b900, #5a8f00);
}

.cert-icon.aws {
    background: linear-gradient(135deg, #ff9900, #cc7700);
}

.cert-details h4 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.cert-details p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Contact Section */
.contact {
    background: var(--bg-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border-radius: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.contact-details h4 {
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.contact-details p {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.facts-list li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.contact-form {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.contact-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.contact-form:hover::before {
    opacity: 1;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
}

.form-group input:hover,
.form-group textarea:hover {
    border-color: rgba(59, 130, 246, 0.5);
    transform: translateY(-1px);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--bg-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-primary);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content,
    .skills-content,
    .contact-content,
    .education-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .timeline::before {
        left: 20px;
    }

    .timeline-content {
        margin-left: 60px;
        width: calc(100% - 60px);
    }

    .timeline-marker {
        left: 20px;
    }

    /* Enhanced Mobile Responsiveness for Projects */
    .projects-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-top: 2rem;
    }

    .project-card {
        max-width: 100%;
        min-height: auto;
    }

    .project-card:hover {
        transform: translateY(-5px) scale(1.01);
    }

    .project-links {
        flex-direction: column;
        gap: 0.75rem;
    }

    .project-link {
        padding: 1rem;
        font-size: 0.95rem;
    }

    .project-icon {
        width: 65px;
        height: 65px;
        font-size: 1.6rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .container {
        padding: 0 1rem;
    }

    /* Disable custom cursor on mobile */
    .custom-cursor,
    .cursor-follower {
        display: none;
    }

    body {
        cursor: auto;
    }

    /* Reduce animations on mobile for better performance */
    .floating-shape {
        animation: none;
    }

    .particles-container {
        display: none;
    }

    /* Simplify hover effects on mobile */
    .project-card:hover {
        transform: translateY(-5px);
    }

    .floating-icon {
        animation-duration: 4s;
    }

    /* Improve touch targets */
    .btn {
        min-height: 44px;
        padding: 1rem 2rem;
    }

    .social-icon {
        width: 60px;
        height: 60px;
    }

    .nav-link {
        padding: 1rem;
    }
}
